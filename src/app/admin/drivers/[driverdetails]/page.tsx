'use client';

import React, { useEffect } from 'react';
import Driverdetails from '../components/Driverdetails';
import { useHeaderTitle } from '@/context/HeaderTitleContext';

export default function Page() {
  const { setTitle } = useHeaderTitle();
  useEffect(() => {
    setTitle('Driver Details');
  }, []);
  return (
    <div className="px-[45px] py-[31px]">
      <Driverdetails />
    </div>
  );
}
