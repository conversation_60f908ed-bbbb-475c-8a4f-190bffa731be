import { backendApiClient, sendMultipart } from '@/utils/apiClient';
import { DriverResponseType } from '@/utils/types';
export async function getTableDriverDetails() {
  const rawResponse = await backendApiClient
    .get('driver-management/driver/get-drivers')
    .json<DriverResponseType>()
    .then(response => response);
  return rawResponse;
}

export async function addTableDriverDetails(payload: any) {
  console.log(payload, '12121212212');
  const rawResponse = await sendMultipart(
    'driver-management/driver/create-driver',
    payload
  );
  return rawResponse;
}
export async function getSpecificDriverDetails(id: string) {
  const rawResponse: any = await backendApiClient
    .get(`driver-management/driver/get-driver/${id}`)
    .json()
    .then(response => response);
  return rawResponse;
}

export const deleteDriver = (payload: string) =>
  backendApiClient
    .delete(`driver-management/driver/delete-driver/${payload}`)
    .json();

export const disableDriverQuery = (payload: any) =>
  backendApiClient
    .patch(
      `driver-management/driver/change-driver-status/${payload?.driverId}`,
      {
        json: { driverStatus: payload?.status },
      }
    )
    .json();

export const downloadReport = (payload: string) =>
  backendApiClient
    .get(`driver-management/driver/export-driver/csv/${payload}`)
    .blob();

export const editDriverDetails = (payload: FormData, id: string) =>
  backendApiClient
    .put(`driver-management/driver/update-driver/${id}`, {
      body: payload,
    })
    .json();

export const fetchDriverSupportLogs = (driverId: string) => {
  return backendApiClient.get(`driver-support/driver/${driverId}`).json();
};

export const fetchDriverActivityLogs = (driverId: string) => {
  return backendApiClient.get(`driver-activity/${driverId}`).json();
};

export const fetchDriverEarnings = (driverId: string) => {
  return backendApiClient.get(`driver-activity/earnings/${driverId}`).json();
};
