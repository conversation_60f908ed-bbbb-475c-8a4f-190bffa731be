'use client';
import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useState } from 'react';
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Field,
  Label,
  Radio,
  RadioGroup,
} from '@headlessui/react';
import Tippy from '@tippyjs/react';
import { hideAll } from 'tippy.js';
import 'tippy.js/dist/tippy.css';
import { PiClockCounterClockwiseLight } from 'react-icons/pi';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  addTableDriverDetails,
  deleteDriver,
  disableDriverQuery,
  downloadReport,
  getTableDriverDetails,
} from '../state/queries';
import { ChevronDownIcon, DownloadIcon } from '@/icons';
import { queryClient } from '@/hooks/useGlobalContext';
import { toast } from 'react-toastify';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { IoMdClose } from 'react-icons/io';
import { IoIosSearch } from 'react-icons/io';
import { FaPlus } from 'react-icons/fa6';
import { subYears } from 'date-fns';
import Input from '@/components/form/input/InputField';
import Select from '@/components/form/Select';
import { PassengersDetailsTypeResponse } from '@/utils/types';

type PassengersDetailsType = {
  fullName: string;
  email: string;
  gender: string;
  dob: Date | null;
  nationalIdSsn: string;
  vehicleAvailability: string;
  model: string;
  plateNumber: string;
  vehicleType: string;
  address: string;
  [key: string]: string | Date | null;
};

const modalYearOptions = Array.from({ length: 2026 - 2000 }, (_, i) => ({
  value: `${2000 + i}`,
  label: `${2000 + i}`,
}));

export default function PassengersTable() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState<string>('');

  const [isSubmitting, setIsSubmitting] = useState(false);

  const [driverType, setDriverType] = useState('company-driver');
  const { data: driverData, isLoading } = useQuery({
    queryKey: ['driverData'],
    queryFn: async () => {
      return getTableDriverDetails();
    },
    enabled: true,
  });

  const [formData, setFormData] = useState<PassengersDetailsType>({
    fullName: '',
    email: '',
    gender: '',
    dob: null,
    nationalIdSsn: '',
    vehicleAvailability: '',
    model: '',
    plateNumber: '',
    vehicleType: '',
    address: '',
    countryCode: '+1',
    contactNo: '9876543210',
    shift: 'morning',
    region: 'North',
    rating: '4.5',
    city: 'demo',
    state: 'demo',
    zipCode: 'demo',
    color: 'Red',
    lastActive: '02-02-2025',
    driverStatus: 'Active',
    insuranceExpiryDate: null,
    insuranceRenewalReminder: 'true',
    vehicleRegistration: 'true',
    vehicleDetails: 'vehicleDetails',
    image: '',
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleDriverDob = (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      dob: date,
    }));
  };
  const handleDriverInsurance = (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      insuranceExpiryDate: date ? date.toISOString().split('T')[0] : '',
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    // If changing vehicle availability to "no", clear dependent fields
    if (name === 'vehicleAvailability' && value === 'no') {
      setFormData(prev => ({
        ...prev,
        [name]: value,
        model: '',
        plateNumber: '',
        vehicleType: '',
        insuranceExpiryDate: null,
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const validate = () => {
    const newErrors: Record<string, string> = {};
    const pattern = /^[A-Z]-\d{3}-[A-Z]{2}$/;

    // Fields that are never required
    const neverRequired = [
      'city',
      'state',
      'zipCode',
      'color',
      'lastActive',
      'driverStatus',
      'image',
      'countryCode',
      'contactNo',
      'shift',
      'region',
      'rating',
      'insuranceRenewalReminder',
      'vehicleRegistration',
      'vehicleDetails',
    ];

    // Fields to skip if vehicleAvailability === 'no'
    const skipIfNoVehicle = [
      'model',
      'plateNumber',
      'vehicleType',
      'insuranceExpiryDate',
    ];

    const shouldSkipVehicleFields = formData.vehicleAvailability === 'no';

    for (const key in formData) {
      // Skip fields that are never required
      if (neverRequired.includes(key)) continue;

      // Skip vehicle-related fields if vehicle availability is 'no'
      if (shouldSkipVehicleFields && skipIfNoVehicle.includes(key)) continue;

      // Validate all other required fields
      if (!formData[key]) {
        newErrors[key] = 'This field is required';
      }

      if (key == 'plateNumber' && !pattern.test(formData[key].toString())) {
        newErrors[key] = 'Please enter a valid code in the format: A-001-BB';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const [deleteProfile, setDeleteProfile] = useState(false);

  const validateFiles = () => {
    console.log('File validation started.');

    const newErrors: Record<string, string> = {};

    setErrors(newErrors);

    const isValid = Object.keys(newErrors).length === 0;

    if (!isValid) {
      console.table(newErrors);
    }

    return isValid;
  };

  const handleSubmit = () => {
    console.log('Form submission started.');
    setIsSubmitting(true);

    const isValid = validate();
    console.log('Validation result:', isValid);

    if (!isValid) {
      console.warn('Validation failed. Submission aborted.');
      setIsSubmitting(false);
      return;
    }
  };

  const addDriverMutation = useMutation({
    mutationFn: (data: any) => {
      return addTableDriverDetails(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverData'] });
      toast.success('Driver Added Successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
      setOpen(false);
      setErrors({});

      setFormData({
        fullName: '',
        email: '',
        gender: '',
        dob: null,
        nationalIdSsn: '',
        vehicleAvailability: '',
        model: '',
        plateNumber: '',
        vehicleType: '',
        address: '',
        countryCode: '+1',
        contactNo: '9876543210',
        shift: 'morning',
        region: 'North',
        rating: '4.5',
        city: 'demo',
        state: 'demo',
        zipCode: 'demo',
        color: 'Red',
        lastActive: '02-02-2025',
        driverStatus: 'Active',
        insuranceExpiryDate: null,
        insuranceRenewalReminder: 'true',
        vehicleRegistration: 'true',
        vehicleDetails: 'vehicleDetails',
        image: '',
      });
    },
    onError: err => {
      console.error(err);
    },
  });

  const handleFormSubmit = () => {
    if (validateFiles()) {
      setIsSubmitting(true); // Start loading

      const payLoad = {
        ...formData,

        driverType: driverType,
      };

      addDriverMutation.mutate(payLoad, {
        onSettled: () => {
          setIsSubmitting(false); // Stop loading when mutation completes (success or error)
        },
      });
    }
  };

  const downLoadDriverMutation = useMutation({
    mutationFn: (id: string) => downloadReport(id),
    onSuccess: async res => {
      const url = window.URL.createObjectURL(
        new Blob([res], { type: 'text/csv' })
      );
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'data.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
    onError: err => {
      console.error('Download error', err);
    },
  });
  const deleteDriverMutation = useMutation({
    mutationFn: (data: string) => {
      return deleteDriver(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverData'] });
      setDeleteProfile(false);
      toast.success('Driver Deleted Successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
    },
    onError: err => {
      console.error(err);
    },
  });
  const disableDriverMutation = useMutation({
    mutationFn: async (data: any) => {
      return disableDriverQuery(data);
    },
    onSuccess: (data: { driverStatus: string }) => {
      queryClient.invalidateQueries({ queryKey: ['driverData'] });
      toast.success(`Driver ${data?.driverStatus} Successfully`, {
        autoClose: 5000,
        position: 'top-center',
      });
    },
    onError: err => {
      console.error(err);
    },
  });

  const handleDisableDriver = (driverId: string, status: string) => {
    const data = { driverId, status };
    if (data) {
      disableDriverMutation.mutate(data);
    }
  };

  const getDriverStatusClass = status => {
    if (status === 'Active') return 'text-[#13BB76]';
    if (status === 'Inactive') return 'text-[#8F8CD6]';
    if (status === 'Suspended') return 'text-[#FF4032]';
    if (status === 'Enroute') return 'text-[#1E90FF]';
    return 'text-[#FF8C00]';
  };

  return (
    <>
      <div className="tabled">
        <div className="flex justify-end">
          <button
            onClick={() => setOpen(true)}
            type="button"
            className="bg-cstm-blue-700 me-2 mb-4 flex items-center gap-2 rounded-full px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
          >
            <FaPlus />
            Add New Passenger
          </button>
        </div>
        <div className="overflow-visible rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
          <div className="header-bar bg-table-head flex items-center justify-between rounded-t-[12px] px-3 py-1">
            {/* Search Bar */}
            <form className="max-w-md flex-1">
              <label className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
                Search
              </label>
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3">
                  <IoIosSearch className="h-[20px] w-[20px] text-[#050013]" />
                </div>
                <input
                  type="search"
                  id="default-search"
                  className="block w-3/4 rounded-full border  border-transparent bg-white p-2 ps-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500 dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                  placeholder="Search here"
                  onChange={e => setSearchTerm(e.target.value)}
                />
              </div>
            </form>
            {/* Buttons Container */}
            <div className="flex items-center gap-2">
              <button
                type="button"
                aria-label="clock"
                className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
              >
                <PiClockCounterClockwiseLight size={24} />
              </button>
              <button
                aria-label="download"
                type="button"
                className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
              >
                <DownloadIcon />
              </button>
            </div>
          </div>
          <div className="custom-scrollbar max-w-full overflow-x-auto">
            {/* <div className="max-w-[1082px] min-w-[-webkit-fill-available]"> */}
            <div className="max-w-[992px] min-w-[-webkit-fill-available]">
              <Table>
                {/* Table Header */}
                <TableHeader className="border-b border-gray-100 bg-white text-[#76787A] dark:border-white/[0.05]">
                  <TableRow>
                    <TableCell
                      isHeader
                      className="text-76787A text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap dark:text-gray-400"
                    >
                      ID
                    </TableCell>
                    <TableCell
                      isHeader
                      className="text-76787A text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap dark:text-gray-400"
                    >
                      Name
                    </TableCell>
                    <TableCell
                      isHeader
                      className="text-76787A text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap dark:text-gray-400"
                    >
                      Contact No
                    </TableCell>
                    <TableCell
                      isHeader
                      className="text-76787A text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap dark:text-gray-400"
                    >
                      Status
                    </TableCell>
                    <TableCell
                      isHeader
                      className="text-76787A text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap dark:text-gray-400"
                    >
                      City
                    </TableCell>
                    <TableCell
                      isHeader
                      className="text-76787A text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap dark:text-gray-400"
                    >
                      Rides
                    </TableCell>
                    <TableCell
                      isHeader
                      className="text-76787A text-theme-xs px-4 py-3 text-start text-[12px] font-medium whitespace-nowrap dark:text-gray-400"
                    >
                      Company
                    </TableCell>
                  </TableRow>
                </TableHeader>
                {/* Table Body */}
                <TableBody className="divide-y bg-white dark:divide-white/[0.05]">
                  {isLoading ? (
                    <TableRow>
                      <TableCell className="py-8 text-center">
                        Loading Passengers...
                      </TableCell>
                    </TableRow>
                  ) : !driverData ? (
                    <TableRow>
                      <TableCell className="py-8 text-center justify-center">
                        No Passengers found
                      </TableCell>
                    </TableRow>
                  ) : (
                    (driverData as PassengersDetailsTypeResponse[])?.map(
                      (order: PassengersDetailsTypeResponse, index: number) => (
                        <TableRow
                          key={index}
                          className="cursor-pointer dark:hover:bg-gray-800"
                        >
                          <TableCell className="px-4 py-3">
                            <p className="text-xs text-[#050013]">
                              #{order?.ID}
                            </p>
                          </TableCell>

                          <TableCell className="px-4 py-3">
                            <p
                              className="text-[12px] text-[#050013] capitalize"
                              onClick={() =>
                                router.push(`passengers/${order?.ID}`)
                              }
                            >
                              {order?.Name}
                            </p>
                          </TableCell>
                          <TableCell className="px-4 py-3">
                            <p className="text-[12px] text-[#050013]">
                              {order?.ContactNo}
                            </p>
                          </TableCell>

                          <TableCell className="px-4 py-3">
                            <p
                              className={`text-[13px] capitalize ${getDriverStatusClass(
                                order?.Status
                              )}`}
                            >
                              {order?.Status}
                            </p>
                          </TableCell>

                          <TableCell className="px-4 py-3">
                            <p className="text-[12px] text-[#050013] uppercase">
                              {order?.City}
                            </p>
                          </TableCell>
                          <TableCell className="px-4 py-3">
                            <p className="text-[12px] text-[#050013]">
                              {order?.Rides}
                            </p>
                          </TableCell>

                          <TableCell className="px-4 py-3">
                            <Tippy
                              trigger="click"
                              content={
                                <div className="bg-white text-gray-900">
                                  <div className="flex flex-col space-y-1 p-1">
                                    <button
                                      className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
                                      onClick={() =>
                                        router.push(
                                          `/passengers/${order?.ID}?edit=true`
                                        )
                                      }
                                    >
                                      Edit Profile
                                    </button>
                                    <button
                                      onClick={() =>
                                        handleDisableDriver(
                                          order?.ID,
                                          'Suspended'
                                        )
                                      }
                                      className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
                                    >
                                      Suspend Driver
                                    </button>
                                    <button
                                      onClick={() =>
                                        handleDisableDriver(order?.ID, 'Active')
                                      }
                                      className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
                                    >
                                      Reinstate Driver
                                    </button>
                                    <button
                                      onClick={() =>
                                        handleDisableDriver(
                                          order?.ID,
                                          'Inactive'
                                        )
                                      }
                                      className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
                                    >
                                      Deactivate Driver
                                    </button>
                                    <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                      Approve Document
                                    </button>
                                    <button
                                      className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
                                      onClick={() =>
                                        toast.success('Notification sent!', {
                                          autoClose: 5000,
                                          position: 'top-center',
                                        })
                                      }
                                    >
                                      Send Notification
                                    </button>
                                    <button
                                      onClick={() => {
                                        downLoadDriverMutation.mutate(
                                          order?.ID
                                        );
                                      }}
                                      className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
                                    >
                                      Download Report
                                    </button>
                                    <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                                      Trip Management
                                    </button>

                                    <button
                                      onClick={() => {
                                        setDeleteProfile(true);
                                        hideAll();
                                      }}
                                      className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
                                    >
                                      Delete Profile
                                    </button>
                                  </div>
                                </div>
                              }
                              interactive={true}
                              placement="right"
                              theme="light"
                              arrow={false}
                              duration={0}
                              className="rounded-lg border border-gray-200 !bg-white !text-gray-900 shadow-sm"
                            >
                              <button
                                type="button"
                                className="text-[#76787A] focus:outline-none"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  className="size-6"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M10.5 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                              </button>
                            </Tippy>
                          </TableCell>
                        </TableRow>
                      )
                    )
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
          <Dialog
            open={open}
            onClose={setOpen}
            className="relative z-100000"
            style={{ borderTopLeftRadius: '50px' }}
          >
            <DialogBackdrop
              transition
              className="fixed inset-0 bg-[#2a2a2a] opacity-60 transition-opacity duration-500 ease-in-out data-closed:opacity-0"
            />
            <div className="fixed inset-0 overflow-hidden">
              <div className="absolute inset-0 overflow-hidden">
                <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                  <DialogPanel
                    transition
                    style={{ borderTopLeftRadius: '50px !important' }}
                    className="pointer-events-auto relative w-full max-w-4xl transform rounded-tl-[50px] transition duration-500 ease-in-out data-closed:translate-x-full sm:w-[500px] sm:duration-700 md:w-[700px] lg:w-[500px] xl:w-[600px]"
                  >
                    <div className="flex h-full flex-col overflow-y-scroll rounded-2xl bg-white shadow-xl">
                      <div className="bg-[#F6F8FB] py-8 sm:px-6">
                        <DialogTitle className="align-center flex items-center justify-between  text-[20px] font-semibold text-gray-900">
                          <div>Passengers Details</div>
                          <IoMdClose
                            className="text-[#76787A] hover:text-[#3324E3]"
                            size={20}
                            onClick={() => setOpen(false)}
                          />
                        </DialogTitle>
                      </div>
                      <div className="custom-scrollbar relative mb-6 flex-1 overflow-y-scroll px-4 sm:px-6">
                        <div className="mt-5 sm:mt-8">
                          <div className="flex items-center justify-center rounded-xl border-gray-200">
                            <div className="w-full">
                              <p className="mb-3 text-sm font-medium text-[#050013] dark:text-white">
                                Profile Overiew
                              </p>

                              <RadioGroup
                                value={driverType}
                                onChange={setDriverType}
                                aria-label="Server size"
                                className="mb-3"
                              >
                                <Field className="flex items-center gap-4 py-2">
                                  <Radio
                                    value="company-driver"
                                    id="company-driver"
                                    className={`group flex size-5 items-center justify-center rounded-full border bg-white data-checked:bg-blue-400 ${
                                      driverType == 'company-driver'
                                        ? 'cstm-radio'
                                        : 'radio-border'
                                    }`}
                                  >
                                    <span className="invisible size-2 rounded-full bg-white group-data-checked:visible" />
                                  </Radio>
                                  <Label
                                    htmlFor="company-driver"
                                    className={`text-[13px] ${
                                      driverType == 'company-driver'
                                        ? 'font-medium text-[#050013]'
                                        : 'font-normal text-[#76787A]'
                                    }`}
                                  >
                                    Individual Passenger
                                  </Label>
                                  <Radio
                                    value="contract-driver"
                                    id="contract-driver"
                                    className={`group flex size-5 items-center justify-center rounded-full border bg-white data-checked:bg-blue-400 ${
                                      driverType == 'contract-driver'
                                        ? 'cstm-radio'
                                        : 'radio-border'
                                    }`}
                                  >
                                    <span className="invisible size-2 rounded-full bg-white group-data-checked:visible" />
                                  </Radio>
                                  <Label
                                    htmlFor="contract-driver"
                                    className={`text-[13px] ${
                                      driverType == 'contract-driver'
                                        ? 'font-medium text-[#050013]'
                                        : 'font-normal text-[#76787A]'
                                    }`}
                                  >
                                    Bulk Passenger
                                  </Label>
                                </Field>
                              </RadioGroup>

                              <div className="pb-2">
                                <Input
                                  type="text"
                                  placeholder="Full Name*"
                                  onChange={handleChange}
                                  name="fullName"
                                />
                                {errors?.fullName && (
                                  <p className="text-sm text-red-500">
                                    {errors?.fullName}
                                  </p>
                                )}
                              </div>

                              <div className="py-2">
                                <Input
                                  type="email"
                                  placeholder="Email ID*"
                                  onChange={e => {
                                    const email = e.target.value;
                                    const isValidEmail =
                                      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

                                    if (isValidEmail) {
                                      setFormData(prev => ({
                                        ...prev,
                                        email,
                                      }));
                                      setErrors(prev => ({
                                        ...prev,
                                        email: '',
                                      }));
                                    } else {
                                      setErrors(prev => ({
                                        ...prev,
                                        email: 'Invalid email address',
                                      }));
                                    }
                                  }}
                                  name="email"
                                />
                                {errors?.email && (
                                  <p className="text-sm text-red-500">
                                    {errors?.email}
                                  </p>
                                )}
                              </div>

                              <div className="py-2">
                                <Input
                                  type="text"
                                  placeholder="Phone Number*"
                                  name="contactNo"
                                />
                                {/* {errors?.email && (
                                  <p className="text-sm text-red-500">
                                    {errors?.email}
                                  </p>
                                )} */}
                              </div>

                              <div className="flex w-full gap-4 py-2">
                                <div className="relative w-full">
                                  <Select
                                    options={[
                                      { value: 'male', label: 'Male' },
                                      { value: 'other', label: 'Other' },
                                      { value: 'female', label: 'Female' },
                                    ]}
                                    placeholder="Gender*"
                                    onChange={e =>
                                      handleSelectChange('gender', e)
                                    }
                                    className="dark:bg-dark-900"
                                  />

                                  <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                                    <ChevronDownIcon />
                                  </span>
                                  {errors?.gender && (
                                    <p className="text-sm text-red-500">
                                      {errors?.gender}
                                    </p>
                                  )}
                                </div>

                                <div className="relative w-full">
                                  <DatePicker
                                    selected={formData?.dob}
                                    className="w-full rounded-lg border border-gray-300 p-2 text-gray-800"
                                    onChange={(date: Date | null) =>
                                      handleDriverDob(date)
                                    }
                                    showYearDropdown
                                    showMonthDropdown
                                    scrollableYearDropdown
                                    dateFormat="dd/MM/yyyy"
                                    placeholderText="DOB"
                                    yearDropdownItemNumber={100}
                                    maxDate={subYears(new Date(), 18)}
                                  />
                                  <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                                    <ChevronDownIcon />
                                  </span>
                                  {errors?.dob && (
                                    <p className="text-sm text-red-500">
                                      {errors?.dob}
                                    </p>
                                  )}
                                </div>
                              </div>

                              <div className="my-3">
                                <h2>Address Details</h2>
                              </div>

                              <Input
                                type="text"
                                placeholder="Address"
                                className="mt-3"
                                // onChange={handleChange}
                                name="Address"
                              />
                              {errors?.nationalIdSsn && (
                                <p className="text-sm text-red-500">
                                  {errors?.nationalIdSsn}
                                </p>
                              )}

                              <p className="mb-3 pt-5 text-sm font-medium text-[#050013] dark:text-white">
                                Payment Details
                              </p>

                              <div className="mt-3 mb-3">
                                <div className="relative">
                                  <Select
                                    options={[
                                      { value: 'yes', label: 'Yes' },
                                      { value: 'no', label: 'No' },
                                    ]}
                                    placeholder="Payment mode"
                                    onChange={e =>
                                      handleSelectChange(
                                        'vehicleAvailability',
                                        e
                                      )
                                    }
                                    className="dark:bg-dark-900"
                                  />

                                  <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                                    <ChevronDownIcon />
                                  </span>
                                </div>
                                {errors?.vehicleAvailability && (
                                  <p className="text-sm text-red-500">
                                    {errors?.vehicleAvailability}
                                  </p>
                                )}
                              </div>

                              {formData.vehicleAvailability === 'yes' && (
                                <>
                                  <div className="mb-3 grid grid-cols-2 gap-2">
                                    <div>
                                      <div className="relative">
                                        <Select
                                          options={modalYearOptions}
                                          placeholder="Model"
                                          onChange={e =>
                                            handleSelectChange('model', e)
                                          }
                                          className="dark:bg-dark-900 w-100"
                                        />
                                        <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                                          <ChevronDownIcon />
                                        </span>
                                      </div>
                                      {errors?.model && (
                                        <p className="text-sm text-red-500">
                                          {errors?.model}
                                        </p>
                                      )}
                                    </div>
                                    <div className="relative">
                                      <div>
                                        <Input
                                          type="text"
                                          placeholder="Plate Number"
                                          className="mb-3"
                                          onChange={handleChange}
                                          name="plateNumber"
                                        />
                                        {errors?.plateNumber && (
                                          <p className="text-sm text-red-500">
                                            {errors?.plateNumber}
                                          </p>
                                        )}
                                        <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                                          <ChevronDownIcon />
                                        </span>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="mb-5 grid grid-cols-2 gap-2">
                                    <div>
                                      <div className="relative w-full">
                                        <DatePicker
                                          selected={
                                            formData.insuranceExpiryDate
                                              ? new Date(
                                                  formData.insuranceExpiryDate
                                                )
                                              : null
                                          }
                                          className="w-full rounded-lg border border-gray-300 p-2 text-gray-800"
                                          onChange={(date: Date | null) =>
                                            handleDriverInsurance(date)
                                          }
                                          showYearDropdown
                                          showMonthDropdown
                                          scrollableYearDropdown
                                          dateFormat="dd/MM/yyyy"
                                          placeholderText="Insurance Expiry Date"
                                          yearDropdownItemNumber={100} // Number of years to show in dropdown
                                          minDate={new Date()}
                                        />
                                        <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                                          <ChevronDownIcon />
                                        </span>
                                      </div>

                                      {errors?.insuranceExpiryDate && (
                                        <p className="text-sm text-red-500">
                                          {errors?.insuranceExpiryDate}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </>
                              )}
                              <div className="relative mb-[40px]">
                                <p className="mb-3 text-sm font-medium text-[#050013] dark:text-white">
                                  Emergency Contact
                                </p>

                                <Input
                                  type="text"
                                  placeholder="Full Name"
                                  className=""
                                  onChange={handleChange}
                                  name="address"
                                />
                                {errors?.address && (
                                  <p className="text-sm text-red-500">
                                    {errors?.address}
                                  </p>
                                )}
                                <span className="pointer-events-none absolute top-[45px] right-3 mt-[10px] -translate-y-1/2 text-[#050013] dark:text-gray-400">
                                  <ChevronDownIcon />
                                </span>

                                <Input
                                  type="text"
                                  placeholder="Phone Number"
                                  className="my-3"
                                  onChange={handleChange}
                                  name="address"
                                />
                                {errors?.address && (
                                  <p className="text-sm text-red-500">
                                    {errors?.address}
                                  </p>
                                )}
                                <span className="pointer-events-none absolute top-[45px] right-3 mt-[10px] -translate-y-1/2 text-[#050013] dark:text-gray-400">
                                  <ChevronDownIcon />
                                </span>
                              </div>

                              <div>
                                <p className="text-sm font-medium text-[#050013] dark:text-white">
                                  Company Details
                                </p>
                                <Input
                                  type="text"
                                  placeholder="Company"
                                  className="mb-3"
                                  onChange={handleChange}
                                  name="plateNumber"
                                />
                                {errors?.plateNumber && (
                                  <p className="text-sm text-red-500">
                                    {errors?.plateNumber}
                                  </p>
                                )}
                                <span className="pointer-events-none absolute top-[22px] right-3 -translate-y-1/2 text-[#050013] dark:text-gray-400">
                                  <ChevronDownIcon />
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="border-grey-700 flex w-full justify-end border-t py-6">
                        <button
                          onClick={handleSubmit}
                          type="button"
                          disabled={isSubmitting}
                          className={`bg-cstm-blue-700 me-2 mb-2 flex items-center gap-2 rounded-full bg-blue-700 px-6 py-3 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 ${
                            isSubmitting ? 'cursor-not-allowed opacity-75' : ''
                          }`}
                        >
                          {isSubmitting ? (
                            <>
                              <svg
                                className="mr-2 -ml-1 h-4 w-4 animate-spin text-white"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  className="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  strokeWidth="4"
                                ></circle>
                                <path
                                  className="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                              </svg>
                              Processing...
                            </>
                          ) : (
                            'Continue'
                          )}
                        </button>
                      </div>
                    </div>
                  </DialogPanel>
                </div>
              </div>
            </div>
          </Dialog>
        </div>
      </div>
    </>
  );
}
