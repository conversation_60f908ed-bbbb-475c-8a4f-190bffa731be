'use client';
import React, { useEffect, useMemo, useState } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { BsDownload } from 'react-icons/bs';
import {
  TbEdit,
  TbNotes,
  TbRotateClockwise2,
  TbFileLike,
} from 'react-icons/tb';
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react';
import {
  CheckboxTickIcon,
  ChevronDownIcon,
  DocumentthumnIcon,
  DollarIcon,
  DollartimeIcon,
  EarningIcon,
  EditIcon,
  StarIcon,
  StarsIcon,
} from '@/icons';
import { CiDollar } from 'react-icons/ci';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import Driveractivitytable from './Driveractivitytable';
import Supportlog from './Supportlog';
import DocumentTab from './Documenttab';
import DeleteModal from '@/components/ui/modal/deletemodal';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  deleteDriver,
  disableDriverQuery,
  downloadReport,
  editDriverDetails,
  getSpecificDriverDetails,
} from '../state/queries';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { queryClient } from '@/hooks/useGlobalContext';
import DisableDriver from './Disabledrive';
import { toast, ToastContainer } from 'react-toastify';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Input from '@/components/form/input/InputField';
import Select from '@/components/form/Select';
import Image from 'next/image';

export default function PassengerDetails() {
  const router = useRouter();
  const params = useParams();

  const driverId = params?.driverdetails as string;

  const [activeTab, setActiveTab] = useState(0);
  const [deleteProfile, setDeleteProfile] = useState(false);
  const [disableDriver, setDisableDriver] = useState(false);
  const [isEditProfile, setIsEditProfile] = useState(false);
  const [driverDetails, setDriverDetails] = useState<any>({});
  const searchParams = useSearchParams();
  const [tippyVisible, setTippyVisible] = useState<boolean>(false);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    gender: '',
    dob: '',
    nationalIdSsn: '',
    vehicleAvailability: '',
    model: '',
    plateNumber: '',
    vehicleType: '',
    address: '',
    countryCode: '+1',
    contactNo: '9876543210',
    shift: 'morning',
    region: 'North',
    rating: '4.5',
    city: '',
    state: '',
    zipCode: '',
    color: '',
    lastActive: '',
    driverStatus: '',
    insuranceExpiryDate: '2030-12-31',
    insuranceRenewalReminder: true,
    vehicleRegistration: 'true',
    vehicleDetails: 'vehicleDetails',
    registrationExpiryDate: '',
    registrationStatus: '',
    image: null as File | null,
  });

  const { data: driverData } = useQuery({
    queryKey: ['driverDetails', driverId],
    queryFn: async () => {
      return getSpecificDriverDetails(driverId);
    },
    enabled: !!driverId,
  });

  const deleteDriverMutation = useMutation({
    mutationFn: (data: string) => {
      return deleteDriver(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverData'] });
      setDeleteProfile(false);
      toast.success('Driver Deleted Successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
    },
    onError: err => {
      console.error(err);
    },
  });
  const disableDriverMutation = useMutation({
    mutationFn: async (data: any) => {
      return disableDriverQuery(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverDetails'] });
      setDisableDriver(false);
      toast.success('Driver Disabled Successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
    },
    onError: err => {
      console.error(err);
    },
  });
  const handleDeleteDriver = () => {
    if (driverId) {
      deleteDriverMutation.mutate(driverId);
      router.push('/driver');
    }
  };
  const handleDisableDriver = () => {
    const data = { driverId, status: 'Inactive' };

    if (driverId) {
      disableDriverMutation.mutate(data);
    }
  };

  const downLoadDriverMutation = useMutation({
    mutationFn: () => downloadReport(driverId),
    onSuccess: async res => {
      const url = window.URL.createObjectURL(
        new Blob([res], { type: 'text/csv' })
      );
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'data.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
    onError: err => {
      console.error('Download error', err);
    },
  });

  const handleChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };
  const handleClick = () => {
    toast.success('Notification sent!', {
      autoClose: 5000,
      position: 'top-center',
    });
  };

  const editDriverDriverMutation = useMutation({
    mutationFn: async (data: FormData) => {
      return editDriverDetails(data, driverId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['driverDetails'] });
      setIsEditProfile(false);
      toast.success('Profile Edited successfully', {
        autoClose: 5000,
        position: 'top-center',
      });
    },
    onError: err => {
      console.error(err);
    },
  });

  const getTippyOptions = () => {
    switch (activeTab) {
      case 0:
        return (
          <div className="bg-white text-gray-900">
            <div className="flex flex-col space-y-1 p-1">
              <DisableDriver
                isOpen={disableDriver}
                setPropTippyVisible={setTippyVisible}
                setIsOpen={setDisableDriver}
                handleDisableDriver={handleDisableDriver}
              />
              <button
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
                onClick={() => {
                  setTippyVisible(false);
                  handleClick;
                }}
              >
                Send Notification
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                <a
                  href={`https://wa.me/${driverDetails?.contactNo}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Send Message
                </a>
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                  downLoadDriverMutation.mutate();
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Export Driver Report
              </button>

              <DeleteModal
                isOpen={deleteProfile}
                // setPropTippyVisible={setTippyVisible}
                setIsOpen={setDeleteProfile}
                handleDeleteDriver={handleDeleteDriver}
                id={driverDetails?.id}
              />
            </div>
          </div>
        );
      case 1: // Activity
        return (
          <div className="bg-white text-gray-900">
            <div className="flex flex-col space-y-1 p-1">
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Bulk Resolve Issues
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Approve All Pending Trips
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Export Activity Data
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Report Suspicious Activity
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Generate Activity Report
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Download Receipts of all Trips
              </button>
            </div>
          </div>
        );
      case 2: // Earning
        return (
          <div className="bg-white text-gray-900">
            <div className="flex flex-col space-y-1 p-1">
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Export Earnings Report
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Adjust Earnings
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Payment History
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Set Payment Schedule
              </button>
            </div>
          </div>
        );
      case 3: // Documents
        return (
          <div className="bg-white text-gray-900">
            <div className="flex flex-col space-y-1 p-1">
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Approve Selected
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Reject Selected
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Send Reminder
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Download
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Export Data
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Update Expiry Date
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Add Comments
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Send Message
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Delete Documents
              </button>
            </div>
          </div>
        );
      case 4: // Support Log
        return (
          <div className="bg-white text-gray-900">
            <div className="flex flex-col space-y-1 p-1">
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                View Details
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Resolve Issue
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Re Opne Issue
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Re-Assign to New Admin
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Escalate Issue
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Add Comment
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Download Log
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Delete Entry
              </button>
            </div>
          </div>
        );
      default:
        return (
          <div className="bg-white text-gray-900">
            <div className="flex flex-col space-y-1 p-1">
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Disable Passenger
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Send Notification
              </button>
              <button className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]">
                <a
                  href={`https://wa.me/${driverDetails?.contactNo}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Send Message
                </a>
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Export Passenger Report
              </button>
              <button
                onClick={() => {
                  setTippyVisible(false);
                }}
                className="w-full rounded px-2 py-1 text-start text-[#76787A] hover:text-[#050013]"
              >
                Delete Profile
              </button>
            </div>
          </div>
        );
    }
  };

  const getDriverStatusClass = status => {
    if (status === 'Active') return 'text-[#13BB76]';
    if (status === 'Inactive') return 'text-[#8F8CD6]';
    if (status === 'Suspended') return 'text-[#FF4032]';
    if (status === 'Enroute') return 'text-[#1E90FF]';
    return 'text-[#FF8C00]';
  };

  useEffect(() => {
    if (driverData) {
      setFormData(driverData[0]);
      setDriverDetails(driverData[0]);
    }
  }, [driverData, driverId]);
  useEffect(() => {
    setIsEditProfile(searchParams.get('edit') === 'true');
  }, [searchParams, params, driverId]);

  const [imageUrl, setImageUrl] = useState('');

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setFormData(prev => ({
      ...prev,
      image: file,
    }));
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImageUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  console.log(activeTab, 'activeTab');

  return (
    <>
      <ToastContainer />
      <div className="space-y-6 rounded-xl border border-gray-200">
        {/* Card Header */}
        <div className="items-top mb-0 flex justify-between rounded-xl rounded-b-none bg-[#ffffff] p-6">
          <div className="flex items-center space-x-4">
            <div className="relative h-24 w-24 sm:h-28 sm:w-28 md:h-32 md:w-32">
              {isEditProfile ? (
                <label
                  htmlFor="profileImageUpload"
                  className="group relative flex h-full w-full cursor-pointer items-center justify-center overflow-visible rounded-full bg-gray-100 shadow-md transition-all duration-200 hover:shadow-lg"
                >
                  <input
                    id="profileImageUpload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageChange}
                  />
                  <div className="absolute right-2 bottom-2 z-[1] rounded-full bg-white p-1.5 shadow-md transition-transform duration-200 group-hover:scale-110">
                    <svg
                      className="h-4 w-4 text-gray-700"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path d="M15.232 5.232l3.536 3.536M9 11l6-6 3 3-6 6H9v-3z" />
                      <path d="M16 16h2a2 2 0 002-2v-1" />
                    </svg>
                  </div>
                  <Image
                    width={96}
                    height={96}
                    src={
                      imageUrl ||
                      driverDetails?.image ||
                      'https://www.twtf.org.uk/wp-content/uploads/2024/01/dummy-image.jpg'
                    }
                    alt="Profile"
                    className="h-full w-full rounded-full object-cover transition-transform duration-200 group-hover:scale-105"
                  />
                </label>
              ) : (
                <Image
                  width={96}
                  height={96}
                  src={
                    driverDetails?.image ||
                    'https://www.twtf.org.uk/wp-content/uploads/2024/01/dummy-image.jpg'
                  }
                  alt="Profile"
                  className="h-full w-full rounded-full object-cover shadow-md"
                />
              )}
            </div>

            <div>
              {isEditProfile ? (
                <Input
                  type="text"
                  placeholder="Full Name"
                  className="mb-3"
                  onChange={handleChange}
                  defaultValue={driverDetails?.fullName}
                  name="fullName"
                />
              ) : (
                <h2 className="font-regular text-[24px] text-[#050013]">
                  {driverDetails?.fullName}
                </h2>
              )}

              <div className="flex items-center space-x-2">
                <span className="bg-active-status h-2 w-2 rounded-full"></span>
                <p
                  className={`text-[11px] ${getDriverStatusClass(
                    driverDetails?.driverStatus
                  )}`}
                >
                  {driverDetails?.driverStatus}
                </p>
              </div>
            </div>
          </div>

          <div className="hover-effect flex items-start gap-3">
            {isEditProfile && activeTab === 0 ? (
              <button
                className="bg-cstm-blue-700 me-2 mb-4 flex items-center gap-2 rounded-full px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                // onClick={handleFormSubmit}
              >
                Save
              </button>
            ) : (
              <button
                className="text-dark-grey rounded-full bg-gray-100 p-2 hover:bg-gray-200"
                onClick={() => {
                  activeTab == 0 && setIsEditProfile(true);
                }}
              >
                <TbEdit size={20} />
              </button>
            )}

            {activeTab != 2 && (
              <>
                <button
                  className="text-dark-grey rounded-full bg-gray-100 p-2 hover:bg-gray-200"
                  onClick={() => downLoadDriverMutation.mutate()}
                >
                  <BsDownload size={20} />
                </button>

                <Tippy
                  className="border border-gray-200 !bg-white !text-gray-900"
                  trigger="click"
                  interactive={true}
                  placement="bottom-end"
                  arrow={false}
                  content={getTippyOptions()}
                  visible={tippyVisible}
                  onClickOutside={() => setTippyVisible(false)}
                >
                  <button
                    type="button"
                    className="text-dark-grey rounded-full bg-gray-100 p-2 hover:bg-gray-200"
                    onClick={() => setTippyVisible(true)}
                  >
                    <BsThreeDotsVertical size={20} />
                  </button>
                </Tippy>
              </>
            )}
          </div>
        </div>

        <TabGroup selectedIndex={activeTab} onChange={setActiveTab}>
          <TabList
            as="aside"
            className="flex gap-[18px] border-t border-gray-200 bg-white px-[43px] pt-5"
          >
            <Tab
              className="mr-[25px] flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]"
              value="generalInfo"
            >
              <TbNotes className="h-[24px] w-[24px]" />
              General Info
            </Tab>
            <Tab
              className="mr-[25px] flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]"
              value="activity"
            >
              <TbRotateClockwise2 className="h-[24px] w-[24px]" />
              Activity
            </Tab>

            <Tab
              className="flex items-center gap-2 pb-[15px] text-sm text-[#76787A] hover:text-[#3324E3] focus:ring-0 focus:outline-none data-[selected]:border-b-[3px] data-[selected]:border-b-[#3324E3] data-[selected]:font-semibold data-[selected]:text-[#3324E3]"
              value="supportLog"
            >
              <TbFileLike className="h-[24px] w-[24px]" />
              Support Log
            </Tab>
          </TabList>

          <TabPanels as="section" className="rounded-b-[12px] bg-[#F6F8FB]">
            <TabPanel>
              <Driveractivitytable driverDetails={driverDetails} />
            </TabPanel>

            <TabPanel>
              <DocumentTab driverDetails={driverDetails} />
            </TabPanel>
            <TabPanel>
              <Supportlog driverDetails={driverDetails} />
            </TabPanel>
          </TabPanels>
        </TabGroup>
      </div>
    </>
  );
}
